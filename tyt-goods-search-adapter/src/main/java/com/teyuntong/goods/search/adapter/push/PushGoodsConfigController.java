package com.teyuntong.goods.search.adapter.push;

import com.teyuntong.goods.search.service.biz.push.dto.PushGoodsConfigSaveDTO;
import com.teyuntong.goods.search.service.biz.push.service.PushGoodsConfigService;
import com.teyuntong.goods.search.service.biz.push.vo.PushGoodsConfigVO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 推送货源配置控制器
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/wechat/push/goods/config")
@Slf4j
public class PushGoodsConfigController {

    private final PushGoodsConfigService pushGoodsConfigService;

    /**
     * 保存推送货源配置
     *
     * @param saveDTO 保存DTO
     * @return 保存结果
     */
    @PostMapping("/save")
    public WebResult<Void> savePushGoodsConfig(@RequestBody PushGoodsConfigSaveDTO saveDTO) {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        Long userId = loginUser.getUserId();
        
        log.info("保存推送货源配置，userId: {}, saveDTO: {}", userId, saveDTO);
        
        pushGoodsConfigService.savePushGoodsConfig(userId, saveDTO);
        
        return WebResult.success();
    }

    /**
     * 查询推送货源配置详情
     *
     * @return 配置详情
     */
    @PostMapping("/detail")
    public WebResult<PushGoodsConfigVO> getPushGoodsConfig() {
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        Long userId = loginUser.getUserId();
        
        log.info("查询推送货源配置详情，userId: {}", userId);
        
        PushGoodsConfigVO config = pushGoodsConfigService.getPushGoodsConfig(userId);
        
        return WebResult.success(config);
    }
}
